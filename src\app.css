@import 'tailwindcss';

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		border-color: var(--color-gray-200, currentColor);
	}
}

@theme {
	--breakpoint-sm: 25rem;
}

:root {
	--background: #ffffff;
	--foreground: #171717;
}

@media (prefers-color-scheme: dark) {
	:root {
		--background: #181818;
		--foreground: #ededed;
	}
}

body {
	color: var(--foreground);
	background: var(--background);
	background-color: rgba(15, 15, 15, 1);
	background-image: linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
		linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
	background-size: 20px 20px;
}
