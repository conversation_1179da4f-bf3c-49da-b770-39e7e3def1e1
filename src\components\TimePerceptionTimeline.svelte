<script lang="ts">
	import { timePerceptionData } from '../lib/timePerceptionData';

	export let currentAge: number = 20;
	export let selectedAge: number | null = null;

	$: displayAge = selectedAge !== null ? selectedAge : currentAge;

	function getColorForIntensity(ageRange: string): string {
		const intensityMap: { [key: string]: string } = {
			"0-5 years": "bg-green-500",
			"6-12 years": "bg-green-400",
			"13-18 years": "bg-yellow-500",
			"19-25 years": "bg-yellow-400",
			"26-35 years": "bg-orange-400",
			"36-45 years": "bg-orange-500",
			"46-55 years": "bg-red-500",
			"56-65 years": "bg-red-600",
			"66-75 years": "bg-orange-500",
			"76-90 years": "bg-yellow-500"
		};
		return intensityMap[ageRange] || "bg-gray-500";
	}

	function isCurrentPeriod(data: any): boolean {
		return displayAge >= data.ageStart && displayAge <= data.ageEnd;
	}
</script>

<div class="w-full rounded-lg border border-white/20 bg-[#141612] p-4">
	<h3 class="mb-4 text-lg font-semibold text-white">Time Perception Throughout Life</h3>
	
	<div class="space-y-2">
		{#each timePerceptionData as data}
			<div class="flex items-center gap-3">
				<!-- Age Range -->
				<div class="w-20 text-sm text-gray-400 text-right">
					{data.ageRange.split(' ')[0]}
				</div>
				
				<!-- Timeline Bar -->
				<div class="flex-1 relative">
					<div 
						class="h-6 rounded-md transition-all duration-300 {getColorForIntensity(data.ageRange)}"
						class:ring-2={isCurrentPeriod(data)}
						class:ring-white={isCurrentPeriod(data)}
						class:opacity-100={isCurrentPeriod(data)}
						class:opacity-60={!isCurrentPeriod(data)}
					>
						{#if isCurrentPeriod(data)}
							<div class="absolute inset-0 flex items-center justify-center">
								<span class="text-xs font-semibold text-white drop-shadow-lg">
									{data.timeFeeling}
								</span>
							</div>
						{/if}
					</div>
				</div>
				
				<!-- Time Feeling -->
				{#if !isCurrentPeriod(data)}
					<div class="w-32 text-sm text-gray-500">
						{data.timeFeeling}
					</div>
				{:else}
					<div class="w-32 text-sm text-white font-semibold">
						Current
					</div>
				{/if}
			</div>
		{/each}
	</div>
	
	<div class="mt-4 text-xs text-gray-500">
		<div class="flex items-center gap-4">
			<div class="flex items-center gap-1">
				<div class="w-3 h-3 bg-green-500 rounded"></div>
				<span>Slow</span>
			</div>
			<div class="flex items-center gap-1">
				<div class="w-3 h-3 bg-yellow-500 rounded"></div>
				<span>Moderate</span>
			</div>
			<div class="flex items-center gap-1">
				<div class="w-3 h-3 bg-orange-500 rounded"></div>
				<span>Fast</span>
			</div>
			<div class="flex items-center gap-1">
				<div class="w-3 h-3 bg-red-500 rounded"></div>
				<span>Very Fast</span>
			</div>
		</div>
	</div>
</div>
