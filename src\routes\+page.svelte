<script lang="ts">
	import Slider from '../components/Slider.svelte';
	import TimePerception from '../components/TimePerception.svelte';
	import AllReferences from '../components/AllReferences.svelte';
	import { getTimePerceptionForAge, getTimePerceptionForMonth } from '../lib/timePerceptionData';

	let age: number = 20;
	const totalMonths: number = 1080;
	const monthsPerRow: number = 36;
	const rows: number = 30;

	$: monthsLived = age * 12;
	let selectedMonthIndex: number | null = null;
	$: selectedMonthIndex = selectedMonthIndex;

	// Time perception reactive statements
	$: currentTimePerception = getTimePerceptionForAge(age);
	$: selectedTimePerception =
		selectedMonthIndex !== null
			? getTimePerceptionForMonth(selectedMonthIndex)
			: currentTimePerception;

	const handleClick = (monthIndex: number) => {
		if (monthIndex === selectedMonthIndex) {
			selectedMonthIndex = null;
			return;
		}
		selectedMonthIndex = monthIndex;
	};
</script>

<!-- Single Responsive Layout -->
<div class="mx-auto flex max-w-6xl flex-col items-center p-4">
	<h1 class="mb-4 text-center text-2xl font-bold lg:mb-6 lg:text-3xl">
		A 90-Year Human Life in Months
	</h1>

	<!-- Controls Section -->
	<div class="mb-6 flex flex-col items-center gap-4 lg:flex-row lg:justify-center lg:gap-6 max-w-4xl">
		<div class="w-full max-w-md rounded-lg border border-white/20 bg-[#141612] p-4 lg:w-80">
			<Slider label="Age" bind:value={age} name="age" min={0} max={90} />
		</div>

		<div class="w-full max-w-md rounded-lg border border-white/20 bg-[#141612] p-4 lg:w-60">
			<span>Selected Month: </span>
			<b>
				{#if selectedMonthIndex}
					{selectedMonthIndex + 1}
				{:else}
					None
				{/if}
			</b>
		</div>
	</div>

	<!-- Life Grid -->
	<div class="mb-4 flex justify-center lg:mb-6 max-w-4xl">
		<div class="flex flex-col gap-[2px]">
			{#each Array(rows) as _, rowIndex}
				<div class="flex gap-[2px]">
					{#each Array(monthsPerRow) as _, colIndex}
						{@const monthIndex = rowIndex * monthsPerRow + colIndex}
						<!-- svelte-ignore a11y-no-noninteractive-tabindex a11y-no-static-element-interactions -->
						<div
							class={`h-1 w-1 cursor-pointer rounded-full transition-all duration-300 sm:h-2 sm:w-2 md:h-3 md:w-3 lg:hover:scale-110 ${monthIndex >= monthsLived ? 'hover:bg-blue-700' : 'hover:bg-red-800'}`}
							class:bg-red-500={monthIndex < monthsLived}
							class:bg-blue-500={monthIndex >= monthsLived}
							class:bg-red-800={selectedMonthIndex === monthIndex && monthIndex < monthsLived}
							class:bg-blue-700={selectedMonthIndex === monthIndex && monthIndex >= monthsLived}
							on:click={() => handleClick(monthIndex)}
							on:keydown={() => handleClick(monthIndex)}
							aria-label="Month Index"
							tabindex={1}
						></div>
					{/each}
				</div>
			{/each}
		</div>
	</div>

	<!-- Statistics -->
	<div class="mt-4 text-center text-sm lg:mb-8 max-w-4xl">
		<p class="text-orange-500">Each row is 36 months = 3 years</p>
		<p>Months lived: {monthsLived}</p>
		<p>Months remaining: {totalMonths - monthsLived}</p>
	</div>

	<!-- Time Perception Information -->
	<div class="mt-6 w-full lg:mb-8">
		<TimePerception
			timePerceptionData={selectedTimePerception}
			currentAge={age}
			selectedMonth={selectedMonthIndex}
		/>
	</div>

	<!-- Educational Section -->
	<div class="mt-6 w-full rounded-lg border border-white/20 bg-[#141612] p-6 lg:mb-8">
		<h3 class="mb-4 text-lg font-semibold text-white">🧠 The Science of Time Perception</h3>
		<div class="space-y-4 text-sm text-gray-300">
			<div>
				<h4 class="mb-2 font-semibold text-blue-400">Why Time Feels Faster as We Age:</h4>
				<ul class="ml-4 space-y-2">
					<li class="flex items-start">
						<span class="mr-2 text-blue-400">•</span>
						<span
							><strong>Proportional Theory:</strong> Each year represents a smaller fraction of your
							total life experience</span
						>
					</li>
					<li class="flex items-start">
						<span class="mr-2 text-blue-400">•</span>
						<span
							><strong>Routine Effect:</strong> Familiar experiences are processed more efficiently by
							the brain</span
						>
					</li>
					<li class="flex items-start">
						<span class="mr-2 text-blue-400">•</span>
						<span
							><strong>Memory Formation:</strong> Fewer novel experiences mean fewer distinct memories
							to mark time</span
						>
					</li>
					<li class="flex items-start">
						<span class="mr-2 text-blue-400">•</span>
						<span
							><strong>Biological Clock:</strong> Our internal circadian rhythms change with age</span
						>
					</li>
				</ul>
			</div>
			<div class="rounded-md border border-purple-500/20 bg-purple-500/10 p-3">
				<p class="text-purple-300">
					<strong>Fun Fact:</strong> When you're 10 years old, one year is 10% of your entire life. When
					you're 50, one year is only 2% of your life experience!
				</p>
			</div>
		</div>
	</div>

	<!-- References Section -->
	<AllReferences />
</div>
