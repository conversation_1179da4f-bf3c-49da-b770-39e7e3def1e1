<script lang="ts">
	import Slider from './Slider.svelte';

	export let age: number = 20;
	export let selectedMonthIndex: number | null = null;
	export let showAdvanced: boolean = false;

	const toggleAdvanced = () => {
		showAdvanced = !showAdvanced;
	};
</script>

<div class="flex flex-col gap-4">
	<!-- Advanced View Toggle -->
	<div class="flex items-center gap-2 text-sm">
		<span class="text-gray-400">Simple</span>
		<button
			on:click={toggleAdvanced}
			class="relative h-4 w-7 rounded-full border border-white/20 transition-colors duration-200"
			class:bg-blue-500={showAdvanced}
			class:bg-gray-600={!showAdvanced}
		>
			<div
				class="absolute top-0.5 h-3 w-3 rounded-full bg-white transition-transform duration-200"
				class:translate-x-3.5={showAdvanced}
				class:translate-x-0.5={!showAdvanced}
			></div>
		</button>
		<span class="text-gray-400">Advanced</span>
	</div>

	<div class="flex flex-col items-center gap-4 lg:flex-row lg:justify-center lg:gap-6">
		<div class="w-full max-w-md rounded-lg border border-white/20 bg-[#141612] p-4 lg:w-60">
			<Slider label="Age" bind:value={age} name="age" min={0} max={90} />
		</div>

		<div class="w-full max-w-md rounded-lg border border-white/20 bg-[#141612] p-4 lg:w-60">
			<span>Selected Month: </span>
			<b>
				{#if selectedMonthIndex}
					{selectedMonthIndex + 1}
				{:else}
					None
				{/if}
			</b>
		</div>
	</div>
</div>
