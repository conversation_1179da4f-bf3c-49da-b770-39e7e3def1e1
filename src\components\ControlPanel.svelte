<script lang="ts">
	import Slider from './Slider.svelte';

	export let age: number = 20;
	export let selectedMonthIndex: number | null = null;
	export let showAdvanced: boolean = false;

	const toggleAdvanced = () => {
		showAdvanced = !showAdvanced;
	};
</script>

<div class="flex flex-col gap-4">
	<!-- Advanced View Toggle -->
	<div class="flex items-center justify-center gap-3">
		<span class="text-sm text-gray-400" class:text-white={!showAdvanced}>Simple</span>
		<button
			on:click={toggleAdvanced}
			class="relative inline-flex h-6 w-11 items-center rounded-full border border-white/20 bg-[#141612] transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#0a0b0a]"
			class:bg-blue-600={showAdvanced}
			class:border-blue-500={showAdvanced}
			class:bg-gray-700={!showAdvanced}
			aria-checked={showAdvanced}
			role="switch"
		>
			<span
				class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-300"
				class:translate-x-6={showAdvanced}
				class:translate-x-1={!showAdvanced}
			></span>
		</button>
		<span class="text-sm text-gray-400" class:text-white={showAdvanced}>Advanced</span>
	</div>

	<!-- Controls -->
	<div class="flex flex-col items-center gap-4 lg:flex-row lg:justify-center lg:gap-6">
		<div class="w-full max-w-md rounded-lg border border-white/20 bg-[#141612] p-4 lg:w-80">
			<Slider label="Age" bind:value={age} name="age" min={0} max={90} />
		</div>

		<div class="w-full max-w-md rounded-lg border border-white/20 bg-[#141612] p-4 lg:w-60">
			<span>Selected Month: </span>
			<b>
				{#if selectedMonthIndex}
					{selectedMonthIndex + 1}
				{:else}
					None
				{/if}
			</b>
		</div>
	</div>
</div>
